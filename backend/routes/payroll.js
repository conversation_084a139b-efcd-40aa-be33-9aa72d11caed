import express from 'express';
import { body, validationResult, param } from 'express-validator';
import { query, transaction } from '../config/database.js';

const router = express.Router();

// Kenyan tax calculation functions
const calculateSHIF = (salary) => {
  return salary * 0.0275; // 2.75% SHIF contribution
};

const calculateNSSF = (salary, employmentType) => {
  if (employmentType === 'CASUAL') return 0;
  return salary * 0.06; // 6% NSSF contribution
};

const calculatePAYE = (salary) => {
  // Kenyan PAYE tax bands (Finance Act 2023)
  if (salary <= 24000) return 0;
  if (salary <= 32333) return (salary - 24000) * 0.1;
  if (salary <= 40385) return 833.3 + (salary - 32333) * 0.15;
  if (salary <= 48462) return 2041.1 + (salary - 40385) * 0.2;
  if (salary <= 56538) return 3656.5 + (salary - 48462) * 0.25;
  return 4675.5 + (salary - 56538) * 0.3;
};

const calculateHousingLevy = (salary) => {
  return salary * 0.015; // 1.5% Housing Levy (employer contribution)
};

// @route   POST /api/payroll/calculate
// @desc    Calculate payroll for an employee
// @access  Private
router.post('/calculate', [
  body('employeeId').isInt().withMessage('Valid employee ID is required'),
  body('basicSalary').isNumeric().withMessage('Basic salary must be a number'),
  body('allowances').optional().isNumeric().withMessage('Allowances must be a number'),
  body('deductions').optional().isNumeric().withMessage('Deductions must be a number'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    const { employeeId, basicSalary, allowances = 0, deductions = 0 } = req.body;

    // Get employee details
    const employeeResult = await query(
      'SELECT employment_type FROM employees WHERE id = $1 AND is_active = true',
      [employeeId]
    );

    if (employeeResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Employee not found or inactive',
      });
    }

    const { employment_type } = employeeResult.rows[0];
    const grossSalary = parseFloat(basicSalary) + parseFloat(allowances);

    // Calculate deductions
    const shif = calculateSHIF(grossSalary);
    const nssf = calculateNSSF(grossSalary, employment_type);
    const paye = calculatePAYE(grossSalary);
    const housingLevy = calculateHousingLevy(grossSalary);

    const totalStatutoryDeductions = shif + nssf + paye;
    const totalDeductions = totalStatutoryDeductions + parseFloat(deductions);
    const netSalary = grossSalary - totalDeductions;

    // Employer contributions
    const employerNSSF = employment_type !== 'CASUAL' ? nssf : 0;

    const payrollCalculation = {
      employeeId: parseInt(employeeId),
      basicSalary: parseFloat(basicSalary),
      allowances: parseFloat(allowances),
      grossSalary,
      deductions: {
        shif: Math.round(shif * 100) / 100,
        nssf: Math.round(nssf * 100) / 100,
        paye: Math.round(paye * 100) / 100,
        other: parseFloat(deductions),
        total: Math.round(totalDeductions * 100) / 100,
      },
      employerContributions: {
        nssf: Math.round(employerNSSF * 100) / 100,
        housingLevy: Math.round(housingLevy * 100) / 100,
      },
      netSalary: Math.round(netSalary * 100) / 100,
      employmentType: employment_type,
    };

    res.json({
      success: true,
      data: {
        payroll: payrollCalculation,
      },
    });
  } catch (error) {
    console.error('Calculate payroll error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// @route   GET /api/payroll/periods
// @desc    Get all payroll periods
// @access  Private
router.get('/periods', async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    const periodsQuery = `
      SELECT id, period_name, start_date, end_date, status, created_at
      FROM payroll_periods
      ORDER BY start_date DESC
      LIMIT $1 OFFSET $2
    `;

    const periods = await query(periodsQuery, [limit, offset]);

    // Get total count
    const countResult = await query('SELECT COUNT(*) as total FROM payroll_periods');
    const total = parseInt(countResult.rows[0].total);

    res.json({
      success: true,
      data: {
        periods: periods.rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error('Get payroll periods error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// @route   POST /api/payroll/periods
// @desc    Create new payroll period
// @access  Private
router.post('/periods', [
  body('periodName').notEmpty().withMessage('Period name is required'),
  body('startDate').isISO8601().withMessage('Valid start date is required'),
  body('endDate').isISO8601().withMessage('Valid end date is required'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    const { periodName, startDate, endDate } = req.body;

    // Check for overlapping periods
    const overlapCheck = await query(
      `SELECT id FROM payroll_periods 
       WHERE (start_date <= $1 AND end_date >= $1) 
       OR (start_date <= $2 AND end_date >= $2)
       OR (start_date >= $1 AND end_date <= $2)`,
      [startDate, endDate]
    );

    if (overlapCheck.rows.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Payroll period overlaps with existing period',
      });
    }

    const insertQuery = `
      INSERT INTO payroll_periods (period_name, start_date, end_date)
      VALUES ($1, $2, $3)
      RETURNING id, period_name, start_date, end_date, status, created_at
    `;

    const result = await query(insertQuery, [periodName, startDate, endDate]);

    res.status(201).json({
      success: true,
      message: 'Payroll period created successfully',
      data: {
        period: result.rows[0],
      },
    });
  } catch (error) {
    console.error('Create payroll period error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// @route   GET /api/payroll/records
// @desc    Get payroll records
// @access  Private
router.get('/records', async (req, res) => {
  try {
    const { page = 1, limit = 10, periodId, employeeId } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE 1=1';
    const params = [];
    let paramCount = 0;

    if (periodId) {
      paramCount++;
      whereClause += ` AND pr.payroll_period_id = $${paramCount}`;
      params.push(periodId);
    }

    if (employeeId) {
      paramCount++;
      whereClause += ` AND pr.employee_id = $${paramCount}`;
      params.push(employeeId);
    }

    const recordsQuery = `
      SELECT 
        pr.id, pr.employee_id, pr.payroll_period_id,
        pr.basic_salary, pr.gross_salary, pr.paye_tax,
        pr.nssf_employee, pr.nssf_employer, pr.shif_contribution,
        pr.housing_levy, pr.total_deductions, pr.net_salary,
        pr.created_at, pr.updated_at,
        e.first_name, e.last_name, e.employee_number,
        pp.period_name, pp.start_date, pp.end_date
      FROM payroll_records pr
      JOIN employees e ON pr.employee_id = e.id
      JOIN payroll_periods pp ON pr.payroll_period_id = pp.id
      ${whereClause}
      ORDER BY pr.created_at DESC
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `;

    params.push(limit, offset);

    const records = await query(recordsQuery, params);

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM payroll_records pr
      ${whereClause}
    `;
    const countParams = params.slice(0, -2);
    const totalResult = await query(countQuery, countParams);
    const total = parseInt(totalResult.rows[0].total);

    res.json({
      success: true,
      data: {
        records: records.rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error('Get payroll records error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

export default router;
