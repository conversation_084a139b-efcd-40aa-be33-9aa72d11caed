import express from 'express';
import { body, validationResult, param } from 'express-validator';
import { query } from '../config/database.js';

const router = express.Router();

// Validation rules
const employeeValidation = [
  body('firstName').notEmpty().withMessage('First name is required'),
  body('lastName').notEmpty().withMessage('Last name is required'),
  body('nationalId').notEmpty().withMessage('National ID is required'),
  body('employmentType').isIn(['PERMANENT', 'CONTRACT', 'CASUAL']).withMessage('Invalid employment type'),
  body('basicSalary').isNumeric().withMessage('Basic salary must be a number'),
  body('email').optional().isEmail().withMessage('Valid email is required'),
  body('phoneNumber').optional().isMobilePhone().withMessage('Valid phone number is required'),
];

// @route   GET /api/employees
// @desc    Get all employees
// @access  Private
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '', department = '', employmentType = '' } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE 1=1';
    const params = [];
    let paramCount = 0;

    if (search) {
      paramCount++;
      whereClause += ` AND (e.first_name ILIKE $${paramCount} OR e.last_name ILIKE $${paramCount} OR e.employee_number ILIKE $${paramCount})`;
      params.push(`%${search}%`);
    }

    if (department) {
      paramCount++;
      whereClause += ` AND d.name = $${paramCount}`;
      params.push(department);
    }

    if (employmentType) {
      paramCount++;
      whereClause += ` AND e.employment_type = $${paramCount}`;
      params.push(employmentType);
    }

    const employeesQuery = `
      SELECT 
        e.id, e.employee_number, e.first_name, e.middle_name, e.last_name,
        e.national_id, e.phone_number, e.email, e.gender, e.date_of_birth,
        e.address, e.employment_type, e.date_hired, e.basic_salary,
        e.bank_name, e.bank_code, e.bank_branch, e.account_number,
        e.kra_pin, e.nssf_number, e.shif_number, e.is_active,
        d.name as department_name, jt.title as job_title,
        e.created_at, e.updated_at
      FROM employees e
      LEFT JOIN departments d ON e.department_id = d.id
      LEFT JOIN job_titles jt ON e.job_title_id = jt.id
      ${whereClause}
      ORDER BY e.created_at DESC
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `;

    params.push(limit, offset);

    const employees = await query(employeesQuery, params);

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM employees e
      LEFT JOIN departments d ON e.department_id = d.id
      ${whereClause}
    `;
    const countParams = params.slice(0, -2); // Remove limit and offset
    const totalResult = await query(countQuery, countParams);
    const total = parseInt(totalResult.rows[0].total);

    res.json({
      success: true,
      data: {
        employees: employees.rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error('Get employees error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// @route   GET /api/employees/:id
// @desc    Get employee by ID
// @access  Private
router.get('/:id', param('id').isInt(), async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid employee ID',
        errors: errors.array(),
      });
    }

    const { id } = req.params;

    const employeeQuery = `
      SELECT 
        e.id, e.employee_number, e.first_name, e.middle_name, e.last_name,
        e.national_id, e.phone_number, e.email, e.gender, e.date_of_birth,
        e.address, e.employment_type, e.date_hired, e.basic_salary,
        e.bank_name, e.bank_code, e.bank_branch, e.account_number,
        e.kra_pin, e.nssf_number, e.shif_number, e.is_active,
        d.name as department_name, jt.title as job_title,
        e.created_at, e.updated_at
      FROM employees e
      LEFT JOIN departments d ON e.department_id = d.id
      LEFT JOIN job_titles jt ON e.job_title_id = jt.id
      WHERE e.id = $1
    `;

    const result = await query(employeeQuery, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Employee not found',
      });
    }

    res.json({
      success: true,
      data: {
        employee: result.rows[0],
      },
    });
  } catch (error) {
    console.error('Get employee error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// @route   POST /api/employees
// @desc    Create new employee
// @access  Private
router.post('/', employeeValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    const {
      firstName,
      middleName,
      lastName,
      nationalId,
      phoneNumber,
      email,
      gender,
      dateOfBirth,
      address,
      departmentId,
      jobTitleId,
      employmentType,
      dateHired,
      basicSalary,
      bankName,
      bankCode,
      bankBranch,
      accountNumber,
      kraPin,
      nssfNumber,
      shifNumber,
    } = req.body;

    // Generate employee number
    const employeeNumberResult = await query(
      'SELECT COALESCE(MAX(CAST(SUBSTRING(employee_number FROM 4) AS INTEGER)), 0) + 1 as next_number FROM employees WHERE employee_number ~ \'^EMP[0-9]+$\''
    );
    const nextNumber = employeeNumberResult.rows[0].next_number;
    const employeeNumber = `EMP${nextNumber.toString().padStart(4, '0')}`;

    // Check if national ID already exists
    const existingEmployee = await query(
      'SELECT id FROM employees WHERE national_id = $1',
      [nationalId]
    );

    if (existingEmployee.rows.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Employee with this National ID already exists',
      });
    }

    const insertQuery = `
      INSERT INTO employees (
        employee_number, first_name, middle_name, last_name, national_id,
        phone_number, email, gender, date_of_birth, address,
        department_id, job_title_id, employment_type, date_hired, basic_salary,
        bank_name, bank_code, bank_branch, account_number,
        kra_pin, nssf_number, shif_number
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10,
        $11, $12, $13, $14, $15, $16, $17, $18, $19,
        $20, $21, $22
      ) RETURNING id
    `;

    const result = await query(insertQuery, [
      employeeNumber, firstName, middleName, lastName, nationalId,
      phoneNumber, email, gender, dateOfBirth, address,
      departmentId, jobTitleId, employmentType, dateHired, basicSalary,
      bankName, bankCode, bankBranch, accountNumber,
      kraPin, nssfNumber, shifNumber
    ]);

    res.status(201).json({
      success: true,
      message: 'Employee created successfully',
      data: {
        employeeId: result.rows[0].id,
        employeeNumber,
      },
    });
  } catch (error) {
    console.error('Create employee error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// @route   PUT /api/employees/:id
// @desc    Update employee
// @access  Private
router.put('/:id', [param('id').isInt(), ...employeeValidation], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    const { id } = req.params;
    const {
      firstName,
      middleName,
      lastName,
      nationalId,
      phoneNumber,
      email,
      gender,
      dateOfBirth,
      address,
      departmentId,
      jobTitleId,
      employmentType,
      dateHired,
      basicSalary,
      bankName,
      bankCode,
      bankBranch,
      accountNumber,
      kraPin,
      nssfNumber,
      shifNumber,
    } = req.body;

    // Check if employee exists
    const existingEmployee = await query('SELECT id FROM employees WHERE id = $1', [id]);
    if (existingEmployee.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Employee not found',
      });
    }

    // Check if national ID already exists for another employee
    const duplicateCheck = await query(
      'SELECT id FROM employees WHERE national_id = $1 AND id != $2',
      [nationalId, id]
    );

    if (duplicateCheck.rows.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Employee with this National ID already exists',
      });
    }

    const updateQuery = `
      UPDATE employees SET
        first_name = $1, middle_name = $2, last_name = $3, national_id = $4,
        phone_number = $5, email = $6, gender = $7, date_of_birth = $8,
        address = $9, department_id = $10, job_title_id = $11,
        employment_type = $12, date_hired = $13, basic_salary = $14,
        bank_name = $15, bank_code = $16, bank_branch = $17,
        account_number = $18, kra_pin = $19, nssf_number = $20,
        shif_number = $21, updated_at = CURRENT_TIMESTAMP
      WHERE id = $22
    `;

    await query(updateQuery, [
      firstName, middleName, lastName, nationalId,
      phoneNumber, email, gender, dateOfBirth, address,
      departmentId, jobTitleId, employmentType, dateHired, basicSalary,
      bankName, bankCode, bankBranch, accountNumber,
      kraPin, nssfNumber, shifNumber, id
    ]);

    res.json({
      success: true,
      message: 'Employee updated successfully',
    });
  } catch (error) {
    console.error('Update employee error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// @route   DELETE /api/employees/:id
// @desc    Delete employee (soft delete)
// @access  Private
router.delete('/:id', param('id').isInt(), async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid employee ID',
        errors: errors.array(),
      });
    }

    const { id } = req.params;

    // Check if employee exists
    const existingEmployee = await query('SELECT id FROM employees WHERE id = $1', [id]);
    if (existingEmployee.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Employee not found',
      });
    }

    // Soft delete by setting is_active to false
    await query(
      'UPDATE employees SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = $1',
      [id]
    );

    res.json({
      success: true,
      message: 'Employee deleted successfully',
    });
  } catch (error) {
    console.error('Delete employee error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// @route   GET /api/employees/departments
// @desc    Get all departments
// @access  Private
router.get('/meta/departments', async (req, res) => {
  try {
    const departments = await query('SELECT id, name FROM departments ORDER BY name');

    res.json({
      success: true,
      data: {
        departments: departments.rows,
      },
    });
  } catch (error) {
    console.error('Get departments error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// @route   GET /api/employees/job-titles
// @desc    Get all job titles
// @access  Private
router.get('/meta/job-titles', async (req, res) => {
  try {
    const jobTitles = await query('SELECT id, title FROM job_titles ORDER BY title');

    res.json({
      success: true,
      data: {
        jobTitles: jobTitles.rows,
      },
    });
  } catch (error) {
    console.error('Get job titles error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

export default router;
