# Environment Configuration
NODE_ENV=development

# Server Configuration
PORT=5000
HOST=localhost

# Database Configuration (PostgreSQL)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=kenyan_payroll_dev
DB_USER=postgres
DB_PASSWORD=password

# Test Database
DB_TEST_NAME=kenyan_payroll_test

# Production Database (use DATABASE_URL for production)
# DATABASE_URL=postgresql://username:password@host:port/database

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:3000

# API Configuration
API_BASE_URL=http://localhost:5000/api

# File Upload Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760

# Email Configuration (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=Kenyan Payroll System

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Session Configuration
SESSION_SECRET=your-session-secret-change-this-in-production

# Kenyan Tax Configuration
KRA_API_URL=https://itax.kra.go.ke/api
NSSF_API_URL=https://www.nssf.or.ke/api
SHIF_API_URL=https://www.sha.go.ke/api

# Application Settings
APP_NAME=Kenyan Payroll Management System
APP_VERSION=1.0.0
SUPPORT_EMAIL=<EMAIL>
