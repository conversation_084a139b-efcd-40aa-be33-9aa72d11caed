import { query, closePool } from '../config/database.js';
import bcrypt from 'bcryptjs';

async function seedDatabase() {
  console.log('🌱 Starting database seeding...');
  
  try {
    // Create admin user
    console.log('👤 Creating admin user...');
    const adminPasswordHash = await bcrypt.hash('password123', 12);
    
    await query(`
      INSERT INTO users (username, email, password_hash, first_name, last_name, is_staff, is_superuser)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      ON CONFLICT (username) DO NOTHING
    `, ['admin', '<EMAIL>', adminPasswordHash, 'System', 'Administrator', true, true]);

    // Create staff user
    console.log('👥 Creating staff user...');
    const staffPasswordHash = await bcrypt.hash('password123', 12);
    
    await query(`
      INSERT INTO users (username, email, password_hash, first_name, last_name, is_staff, is_superuser)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      ON CONFLICT (username) DO NOTHING
    `, ['staff', '<EMAIL>', staffPasswordHash, 'HR', 'Staff', true, false]);

    // Create departments
    console.log('🏢 Creating departments...');
    const departments = [
      { name: 'Administration', description: 'Administrative and management functions' },
      { name: 'Finance', description: 'Financial management and accounting' },
      { name: 'Human Resources', description: 'HR and employee management' },
      { name: 'Information Technology', description: 'IT support and development' },
      { name: 'Operations', description: 'Day-to-day operations' },
      { name: 'Municipality', description: 'Municipal services and governance' },
      { name: 'Ugatuzi', description: 'Devolution and county services' },
    ];

    for (const dept of departments) {
      await query(`
        INSERT INTO departments (name, description)
        VALUES ($1, $2)
        ON CONFLICT (name) DO NOTHING
      `, [dept.name, dept.description]);
    }

    // Create job titles
    console.log('💼 Creating job titles...');
    const jobTitles = [
      { title: 'Manager', description: 'Department manager' },
      { title: 'Assistant Manager', description: 'Assistant to department manager' },
      { title: 'Officer', description: 'General officer position' },
      { title: 'Assistant', description: 'Assistant position' },
      { title: 'Clerk', description: 'Clerical position' },
      { title: 'Secretary', description: 'Secretarial position' },
      { title: 'Driver', description: 'Driver position' },
      { title: 'Security Guard', description: 'Security personnel' },
      { title: 'Casual Worker', description: 'Casual labor position' },
      { title: 'Consultant', description: 'External consultant' },
    ];

    for (const title of jobTitles) {
      await query(`
        INSERT INTO job_titles (title, description)
        VALUES ($1, $2)
        ON CONFLICT (title) DO NOTHING
      `, [title.title, title.description]);
    }

    // Get department and job title IDs for employees
    const deptResult = await query('SELECT id, name FROM departments');
    const titleResult = await query('SELECT id, title FROM job_titles');
    
    const deptMap = {};
    deptResult.rows.forEach(dept => {
      deptMap[dept.name] = dept.id;
    });
    
    const titleMap = {};
    titleResult.rows.forEach(title => {
      titleMap[title.title] = title.id;
    });

    // Create sample employees
    console.log('👨‍💼 Creating sample employees...');
    const employees = [
      {
        firstName: 'John',
        lastName: 'Mwangi',
        nationalId: '********',
        phoneNumber: '+2547********',
        email: '<EMAIL>',
        gender: 'MALE',
        dateOfBirth: '1985-03-15',
        address: 'P.O. Box 123, Nairobi',
        department: 'Administration',
        jobTitle: 'Officer',
        employmentType: 'PERMANENT',
        dateHired: '2020-01-15',
        basicSalary: 50000,
        bankName: 'KCB Bank',
        bankCode: '01',
        bankBranch: 'Nairobi Branch',
        accountNumber: '********90',
        kraPin: 'A001234567B',
        nssfNumber: 'NS001234567',
        shifNumber: 'SH001234567',
      },
      {
        firstName: 'Jane',
        lastName: 'Wanjiku',
        nationalId: '********',
        phoneNumber: '+************',
        email: '<EMAIL>',
        gender: 'FEMALE',
        dateOfBirth: '1988-07-22',
        address: 'P.O. Box 456, Nairobi',
        department: 'Finance',
        jobTitle: 'Manager',
        employmentType: 'PERMANENT',
        dateHired: '2019-03-01',
        basicSalary: 75000,
        bankName: 'Equity Bank',
        bankCode: '68',
        bankBranch: 'Westlands Branch',
        accountNumber: '09********',
        kraPin: 'A007654321C',
        nssfNumber: 'NS007654321',
        shifNumber: 'SH007654321',
      },
      {
        firstName: 'Peter',
        lastName: 'Kiprotich',
        nationalId: '********',
        phoneNumber: '+************',
        email: '<EMAIL>',
        gender: 'MALE',
        dateOfBirth: '1992-11-08',
        address: 'P.O. Box 789, Eldoret',
        department: 'Ugatuzi',
        jobTitle: 'Casual Worker',
        employmentType: 'CASUAL',
        dateHired: '2023-01-01',
        basicSalary: 30000,
        bankName: 'Co-operative Bank',
        bankCode: '11',
        bankBranch: 'Eldoret Branch',
        accountNumber: '********55',
        kraPin: 'A0********D',
        nssfNumber: null,
        shifNumber: 'SH0********',
      },
      {
        firstName: 'Mary',
        lastName: 'Achieng',
        nationalId: '********',
        phoneNumber: '+************',
        email: '<EMAIL>',
        gender: 'FEMALE',
        dateOfBirth: '1990-05-12',
        address: 'P.O. Box 321, Kisumu',
        department: 'Municipality',
        jobTitle: 'Assistant',
        employmentType: 'CONTRACT',
        dateHired: '2022-06-15',
        basicSalary: 45000,
        bankName: 'NCBA Bank',
        bankCode: '07',
        bankBranch: 'Kisumu Branch',
        accountNumber: '********99',
        kraPin: 'A0********E',
        nssfNumber: 'NS0********',
        shifNumber: 'SH0********',
      },
      {
        firstName: 'David',
        lastName: 'Otieno',
        nationalId: '********',
        phoneNumber: '+************',
        email: '<EMAIL>',
        gender: 'MALE',
        dateOfBirth: '1987-09-30',
        address: 'P.O. Box 654, Mombasa',
        department: 'Information Technology',
        jobTitle: 'Manager',
        employmentType: 'PERMANENT',
        dateHired: '2018-11-01',
        basicSalary: 85000,
        bankName: 'Standard Chartered',
        bankCode: '02',
        bankBranch: 'Mombasa Branch',
        accountNumber: '**********',
        kraPin: 'A0********F',
        nssfNumber: 'NS0********',
        shifNumber: 'SH0********',
      },
    ];

    for (const emp of employees) {
      // Generate employee number
      const empNumberResult = await query(
        'SELECT COALESCE(MAX(CAST(SUBSTRING(employee_number FROM 4) AS INTEGER)), 0) + 1 as next_number FROM employees WHERE employee_number ~ \'^EMP[0-9]+$\''
      );
      const nextNumber = empNumberResult.rows[0].next_number;
      const employeeNumber = `EMP${nextNumber.toString().padStart(4, '0')}`;

      await query(`
        INSERT INTO employees (
          employee_number, first_name, last_name, national_id, phone_number, email,
          gender, date_of_birth, address, department_id, job_title_id,
          employment_type, date_hired, basic_salary, bank_name, bank_code,
          bank_branch, account_number, kra_pin, nssf_number, shif_number
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21
        )
        ON CONFLICT (national_id) DO NOTHING
      `, [
        employeeNumber, emp.firstName, emp.lastName, emp.nationalId, emp.phoneNumber,
        emp.email, emp.gender, emp.dateOfBirth, emp.address,
        deptMap[emp.department], titleMap[emp.jobTitle], emp.employmentType,
        emp.dateHired, emp.basicSalary, emp.bankName, emp.bankCode,
        emp.bankBranch, emp.accountNumber, emp.kraPin, emp.nssfNumber, emp.shifNumber
      ]);
    }

    // Create current tax rates
    console.log('💰 Creating tax rates...');
    
    // PAYE tax bands (Finance Act 2023)
    const payeBands = [
      { min: 0, max: 24000, rate: 0 },
      { min: 24001, max: 32333, rate: 10 },
      { min: 32334, max: 40385, rate: 15 },
      { min: 40386, max: 48462, rate: 20 },
      { min: 48463, max: 56538, rate: 25 },
      { min: 56539, max: null, rate: 30 },
    ];

    for (const band of payeBands) {
      await query(`
        INSERT INTO paye_tax_bands (min_amount, max_amount, rate, effective_date)
        VALUES ($1, $2, $3, '2023-07-01')
        ON CONFLICT DO NOTHING
      `, [band.min, band.max, band.rate]);
    }

    // NSSF rates
    await query(`
      INSERT INTO nssf_rates (employee_rate, employer_rate, effective_date)
      VALUES (6.00, 6.00, '2023-01-01')
      ON CONFLICT DO NOTHING
    `);

    // SHIF rates
    await query(`
      INSERT INTO shif_rates (contribution_rate, effective_date)
      VALUES (2.75, '2023-10-01')
      ON CONFLICT DO NOTHING
    `);

    // Housing Levy rates
    await query(`
      INSERT INTO housing_levy_rates (employer_rate, effective_date)
      VALUES (1.50, '2023-03-01')
      ON CONFLICT DO NOTHING
    `);

    console.log('🎉 Database seeding completed successfully!');
    console.log('');
    console.log('📋 Demo Credentials:');
    console.log('   Admin: admin / password123');
    console.log('   Staff: staff / password123');
    console.log('');
    console.log('👥 Sample employees created with Kenyan compliance data');
    
  } catch (error) {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  } finally {
    await closePool();
  }
}

// Run seeding if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  seedDatabase();
}

export { seedDatabase };
