import express from 'express';
import { query } from '../config/database.js';

const router = express.Router();

// @route   GET /api/reports/dashboard
// @desc    Get dashboard statistics
// @access  Private
router.get('/dashboard', async (req, res) => {
  try {
    // Get total employees
    const totalEmployeesResult = await query(
      'SELECT COUNT(*) as total FROM employees WHERE is_active = true'
    );
    const totalEmployees = parseInt(totalEmployeesResult.rows[0].total);

    // Get total payroll for current month
    const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format
    const totalPayrollResult = await query(
      `SELECT COALESCE(SUM(basic_salary), 0) as total 
       FROM employees 
       WHERE is_active = true`
    );
    const totalPayroll = parseFloat(totalPayrollResult.rows[0].total);

    // Calculate total SHIF
    const totalSHIF = totalPayroll * 0.0275;

    // Calculate total NSSF (excluding casual workers)
    const nssfResult = await query(
      `SELECT COALESCE(SUM(basic_salary), 0) as total 
       FROM employees 
       WHERE is_active = true AND employment_type != 'CASUAL'`
    );
    const nssfBaseSalary = parseFloat(nssfResult.rows[0].total);
    const totalNSSF = nssfBaseSalary * 0.06;

    // Calculate total Housing Levy
    const totalHousingLevy = totalPayroll * 0.015;

    // Get employees by department
    const departmentStatsResult = await query(`
      SELECT 
        d.name as department_name,
        COUNT(e.id) as employee_count,
        COALESCE(SUM(e.basic_salary), 0) as total_salary
      FROM departments d
      LEFT JOIN employees e ON d.id = e.department_id AND e.is_active = true
      GROUP BY d.id, d.name
      ORDER BY employee_count DESC
    `);

    // Get employees by employment type
    const employmentTypeStatsResult = await query(`
      SELECT 
        employment_type,
        COUNT(*) as count,
        COALESCE(SUM(basic_salary), 0) as total_salary
      FROM employees 
      WHERE is_active = true
      GROUP BY employment_type
      ORDER BY count DESC
    `);

    // Get recent payroll records
    const recentPayrollResult = await query(`
      SELECT 
        pr.id, pr.net_salary, pr.created_at,
        e.first_name, e.last_name, e.employee_number,
        pp.period_name
      FROM payroll_records pr
      JOIN employees e ON pr.employee_id = e.id
      JOIN payroll_periods pp ON pr.payroll_period_id = pp.id
      ORDER BY pr.created_at DESC
      LIMIT 5
    `);

    res.json({
      success: true,
      data: {
        summary: {
          totalEmployees,
          totalPayroll: Math.round(totalPayroll * 100) / 100,
          totalSHIF: Math.round(totalSHIF * 100) / 100,
          totalNSSF: Math.round(totalNSSF * 100) / 100,
          totalHousingLevy: Math.round(totalHousingLevy * 100) / 100,
        },
        departmentStats: departmentStatsResult.rows,
        employmentTypeStats: employmentTypeStatsResult.rows,
        recentPayroll: recentPayrollResult.rows,
      },
    });
  } catch (error) {
    console.error('Get dashboard stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// @route   GET /api/reports/payroll-summary
// @desc    Get payroll summary report
// @access  Private
router.get('/payroll-summary', async (req, res) => {
  try {
    const { periodId, startDate, endDate } = req.query;

    let whereClause = 'WHERE 1=1';
    const params = [];
    let paramCount = 0;

    if (periodId) {
      paramCount++;
      whereClause += ` AND pr.payroll_period_id = $${paramCount}`;
      params.push(periodId);
    } else if (startDate && endDate) {
      paramCount++;
      whereClause += ` AND pp.start_date >= $${paramCount}`;
      params.push(startDate);
      paramCount++;
      whereClause += ` AND pp.end_date <= $${paramCount}`;
      params.push(endDate);
    }

    const summaryQuery = `
      SELECT 
        COUNT(pr.id) as total_records,
        SUM(pr.basic_salary) as total_basic_salary,
        SUM(pr.gross_salary) as total_gross_salary,
        SUM(pr.paye_tax) as total_paye,
        SUM(pr.nssf_employee) as total_nssf_employee,
        SUM(pr.nssf_employer) as total_nssf_employer,
        SUM(pr.shif_contribution) as total_shif,
        SUM(pr.housing_levy) as total_housing_levy,
        SUM(pr.total_deductions) as total_deductions,
        SUM(pr.net_salary) as total_net_salary
      FROM payroll_records pr
      JOIN payroll_periods pp ON pr.payroll_period_id = pp.id
      ${whereClause}
    `;

    const summaryResult = await query(summaryQuery, params);

    // Get breakdown by department
    const departmentBreakdownQuery = `
      SELECT 
        d.name as department_name,
        COUNT(pr.id) as employee_count,
        SUM(pr.gross_salary) as total_gross,
        SUM(pr.net_salary) as total_net,
        SUM(pr.total_deductions) as total_deductions
      FROM payroll_records pr
      JOIN payroll_periods pp ON pr.payroll_period_id = pp.id
      JOIN employees e ON pr.employee_id = e.id
      JOIN departments d ON e.department_id = d.id
      ${whereClause}
      GROUP BY d.id, d.name
      ORDER BY total_gross DESC
    `;

    const departmentBreakdown = await query(departmentBreakdownQuery, params);

    // Get breakdown by employment type
    const employmentTypeBreakdownQuery = `
      SELECT 
        e.employment_type,
        COUNT(pr.id) as employee_count,
        SUM(pr.gross_salary) as total_gross,
        SUM(pr.net_salary) as total_net,
        SUM(pr.total_deductions) as total_deductions
      FROM payroll_records pr
      JOIN payroll_periods pp ON pr.payroll_period_id = pp.id
      JOIN employees e ON pr.employee_id = e.id
      ${whereClause}
      GROUP BY e.employment_type
      ORDER BY total_gross DESC
    `;

    const employmentTypeBreakdown = await query(employmentTypeBreakdownQuery, params);

    res.json({
      success: true,
      data: {
        summary: summaryResult.rows[0],
        departmentBreakdown: departmentBreakdown.rows,
        employmentTypeBreakdown: employmentTypeBreakdown.rows,
      },
    });
  } catch (error) {
    console.error('Get payroll summary error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// @route   GET /api/reports/tax-summary
// @desc    Get tax summary report
// @access  Private
router.get('/tax-summary', async (req, res) => {
  try {
    const { periodId, year, month } = req.query;

    let whereClause = 'WHERE 1=1';
    const params = [];
    let paramCount = 0;

    if (periodId) {
      paramCount++;
      whereClause += ` AND pr.payroll_period_id = $${paramCount}`;
      params.push(periodId);
    } else if (year && month) {
      paramCount++;
      whereClause += ` AND EXTRACT(YEAR FROM pp.start_date) = $${paramCount}`;
      params.push(year);
      paramCount++;
      whereClause += ` AND EXTRACT(MONTH FROM pp.start_date) = $${paramCount}`;
      params.push(month);
    } else if (year) {
      paramCount++;
      whereClause += ` AND EXTRACT(YEAR FROM pp.start_date) = $${paramCount}`;
      params.push(year);
    }

    const taxSummaryQuery = `
      SELECT 
        SUM(pr.paye_tax) as total_paye,
        SUM(pr.nssf_employee) as total_nssf_employee,
        SUM(pr.nssf_employer) as total_nssf_employer,
        SUM(pr.shif_contribution) as total_shif,
        SUM(pr.housing_levy) as total_housing_levy,
        COUNT(DISTINCT pr.employee_id) as total_employees
      FROM payroll_records pr
      JOIN payroll_periods pp ON pr.payroll_period_id = pp.id
      ${whereClause}
    `;

    const taxSummary = await query(taxSummaryQuery, params);

    // Get monthly breakdown for the year
    let monthlyBreakdown = [];
    if (year) {
      const monthlyQuery = `
        SELECT 
          EXTRACT(MONTH FROM pp.start_date) as month,
          SUM(pr.paye_tax) as paye,
          SUM(pr.nssf_employee) as nssf_employee,
          SUM(pr.nssf_employer) as nssf_employer,
          SUM(pr.shif_contribution) as shif,
          SUM(pr.housing_levy) as housing_levy
        FROM payroll_records pr
        JOIN payroll_periods pp ON pr.payroll_period_id = pp.id
        WHERE EXTRACT(YEAR FROM pp.start_date) = $1
        GROUP BY EXTRACT(MONTH FROM pp.start_date)
        ORDER BY month
      `;

      const monthlyResult = await query(monthlyQuery, [year]);
      monthlyBreakdown = monthlyResult.rows;
    }

    res.json({
      success: true,
      data: {
        summary: taxSummary.rows[0],
        monthlyBreakdown,
      },
    });
  } catch (error) {
    console.error('Get tax summary error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// @route   GET /api/reports/employee-payslips
// @desc    Get employee payslips
// @access  Private
router.get('/employee-payslips', async (req, res) => {
  try {
    const { employeeId, periodId, year } = req.query;

    if (!employeeId) {
      return res.status(400).json({
        success: false,
        message: 'Employee ID is required',
      });
    }

    let whereClause = 'WHERE pr.employee_id = $1';
    const params = [employeeId];
    let paramCount = 1;

    if (periodId) {
      paramCount++;
      whereClause += ` AND pr.payroll_period_id = $${paramCount}`;
      params.push(periodId);
    } else if (year) {
      paramCount++;
      whereClause += ` AND EXTRACT(YEAR FROM pp.start_date) = $${paramCount}`;
      params.push(year);
    }

    const payslipsQuery = `
      SELECT 
        pr.id, pr.basic_salary, pr.gross_salary, pr.paye_tax,
        pr.nssf_employee, pr.nssf_employer, pr.shif_contribution,
        pr.housing_levy, pr.total_deductions, pr.net_salary,
        pr.created_at,
        e.first_name, e.last_name, e.employee_number, e.employment_type,
        pp.period_name, pp.start_date, pp.end_date,
        d.name as department_name, jt.title as job_title
      FROM payroll_records pr
      JOIN employees e ON pr.employee_id = e.id
      JOIN payroll_periods pp ON pr.payroll_period_id = pp.id
      LEFT JOIN departments d ON e.department_id = d.id
      LEFT JOIN job_titles jt ON e.job_title_id = jt.id
      ${whereClause}
      ORDER BY pp.start_date DESC
    `;

    const payslips = await query(payslipsQuery, params);

    res.json({
      success: true,
      data: {
        payslips: payslips.rows,
      },
    });
  } catch (error) {
    console.error('Get employee payslips error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

export default router;
