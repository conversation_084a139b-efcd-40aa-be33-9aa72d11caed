import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { X, BarChart3, Users, DollarSign, FileText, Calculator } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';

const Sidebar = ({ open, setOpen }) => {
  const location = useLocation();
  const { user } = useAuth();

  const navigation = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: BarChart3,
      current: location.pathname === '/dashboard',
    },
    {
      name: 'Employees',
      href: '/employees',
      icon: Users,
      current: location.pathname.startsWith('/employees'),
    },
    {
      name: 'Payroll',
      href: '/payroll',
      icon: DollarSign,
      current: location.pathname === '/payroll',
    },
    {
      name: 'Reports',
      href: '/reports',
      icon: FileText,
      current: location.pathname === '/reports',
    },
  ];

  const NavItem = ({ item }) => (
    <NavLink
      to={item.href}
      onClick={() => setOpen(false)}
      className={({ isActive }) =>
        `group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${
          isActive
            ? 'bg-primary-100 text-primary-900 border-r-2 border-primary-600'
            : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
        }`
      }
    >
      <item.icon
        className={`mr-3 h-5 w-5 flex-shrink-0 ${
          item.current ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-500'
        }`}
      />
      {item.name}
    </NavLink>
  );

  return (
    <>
      {/* Mobile sidebar overlay */}
      {open && (
        <div className="fixed inset-0 z-40 lg:hidden">
          <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setOpen(false)} />
        </div>
      )}

      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${
          open ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between h-16 px-4 bg-primary-600">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center mr-3">
                <span className="text-primary-600 font-bold text-lg">🇰🇪</span>
              </div>
              <h1 className="text-white font-bold text-lg">Payroll System</h1>
            </div>
            <button
              onClick={() => setOpen(false)}
              className="lg:hidden text-white hover:text-gray-200 focus:outline-none focus:ring-2 focus:ring-white"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-1 overflow-y-auto">
            {navigation.map((item) => (
              <NavItem key={item.name} item={item} />
            ))}
          </nav>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center">
                <span className="text-white font-medium text-sm">
                  {user?.first_name?.charAt(0)}{user?.last_name?.charAt(0)}
                </span>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900">
                  {user?.first_name} {user?.last_name}
                </p>
                <p className="text-xs text-gray-500">
                  {user?.is_superuser ? 'Administrator' : user?.is_staff ? 'Staff' : 'User'}
                </p>
              </div>
            </div>
            
            {/* Compliance indicator */}
            <div className="mt-4 p-3 bg-green-50 rounded-lg">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                <span className="text-xs text-green-700 font-medium">KRA Compliant</span>
              </div>
              <p className="text-xs text-green-600 mt-1">
                SHIF • NSSF • PAYE • Housing Levy
              </p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
