import express from 'express';
import { body, validationResult } from 'express-validator';
import { query } from '../config/database.js';

const router = express.Router();

// Kenyan tax calculation functions
const calculateSHIF = (salary) => {
  return salary * 0.0275; // 2.75% SHIF contribution
};

const calculateNSSF = (salary, employmentType) => {
  if (employmentType === 'CASUAL') return 0;
  return salary * 0.06; // 6% NSSF contribution
};

const calculatePAYE = (salary) => {
  // Kenyan PAYE tax bands (Finance Act 2023)
  if (salary <= 24000) return 0;
  if (salary <= 32333) return (salary - 24000) * 0.1;
  if (salary <= 40385) return 833.3 + (salary - 32333) * 0.15;
  if (salary <= 48462) return 2041.1 + (salary - 40385) * 0.2;
  if (salary <= 56538) return 3656.5 + (salary - 48462) * 0.25;
  return 4675.5 + (salary - 56538) * 0.3;
};

const calculateHousingLevy = (salary) => {
  return salary * 0.015; // 1.5% Housing Levy (employer contribution)
};

// @route   POST /api/tax/calculate
// @desc    Calculate taxes for a given salary
// @access  Private
router.post('/calculate', [
  body('salary').isNumeric().withMessage('Salary must be a number'),
  body('employmentType').isIn(['PERMANENT', 'CONTRACT', 'CASUAL']).withMessage('Invalid employment type'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    const { salary, employmentType } = req.body;
    const grossSalary = parseFloat(salary);

    // Calculate all taxes and deductions
    const shif = calculateSHIF(grossSalary);
    const nssf = calculateNSSF(grossSalary, employmentType);
    const paye = calculatePAYE(grossSalary);
    const housingLevy = calculateHousingLevy(grossSalary);

    const totalDeductions = shif + nssf + paye;
    const netSalary = grossSalary - totalDeductions;

    // Employer contributions
    const employerNSSF = employmentType !== 'CASUAL' ? nssf : 0;

    const taxCalculation = {
      grossSalary,
      employmentType,
      deductions: {
        shif: Math.round(shif * 100) / 100,
        nssf: Math.round(nssf * 100) / 100,
        paye: Math.round(paye * 100) / 100,
        total: Math.round(totalDeductions * 100) / 100,
      },
      employerContributions: {
        nssf: Math.round(employerNSSF * 100) / 100,
        housingLevy: Math.round(housingLevy * 100) / 100,
        total: Math.round((employerNSSF + housingLevy) * 100) / 100,
      },
      netSalary: Math.round(netSalary * 100) / 100,
      totalCostToEmployer: Math.round((grossSalary + employerNSSF + housingLevy) * 100) / 100,
    };

    res.json({
      success: true,
      data: {
        calculation: taxCalculation,
      },
    });
  } catch (error) {
    console.error('Calculate tax error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// @route   GET /api/tax/rates
// @desc    Get current tax rates and bands
// @access  Private
router.get('/rates', async (req, res) => {
  try {
    const taxRates = {
      shif: {
        rate: 0.0275,
        description: 'Social Health Insurance Fund',
        applicableToAll: true,
      },
      nssf: {
        employeeRate: 0.06,
        employerRate: 0.06,
        description: 'National Social Security Fund',
        exemptions: ['CASUAL'],
      },
      housingLevy: {
        rate: 0.015,
        description: 'Housing Development Levy',
        paidBy: 'employer',
        applicableToAll: true,
      },
      paye: {
        description: 'Pay As You Earn Tax',
        bands: [
          { min: 0, max: 24000, rate: 0, description: 'Tax-free band' },
          { min: 24001, max: 32333, rate: 0.1, description: '10% tax band' },
          { min: 32334, max: 40385, rate: 0.15, description: '15% tax band' },
          { min: 40386, max: 48462, rate: 0.2, description: '20% tax band' },
          { min: 48463, max: 56538, rate: 0.25, description: '25% tax band' },
          { min: 56539, max: null, rate: 0.3, description: '30% tax band' },
        ],
      },
    };

    res.json({
      success: true,
      data: {
        rates: taxRates,
        lastUpdated: '2023-07-01', // Finance Act 2023
        currency: 'KES',
      },
    });
  } catch (error) {
    console.error('Get tax rates error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// @route   POST /api/tax/bulk-calculate
// @desc    Calculate taxes for multiple employees
// @access  Private
router.post('/bulk-calculate', [
  body('employees').isArray().withMessage('Employees must be an array'),
  body('employees.*.employeeId').isInt().withMessage('Valid employee ID is required'),
  body('employees.*.salary').isNumeric().withMessage('Salary must be a number'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    const { employees } = req.body;
    const calculations = [];

    for (const emp of employees) {
      try {
        // Get employee details
        const employeeResult = await query(
          'SELECT employment_type, first_name, last_name FROM employees WHERE id = $1 AND is_active = true',
          [emp.employeeId]
        );

        if (employeeResult.rows.length === 0) {
          calculations.push({
            employeeId: emp.employeeId,
            error: 'Employee not found or inactive',
          });
          continue;
        }

        const { employment_type, first_name, last_name } = employeeResult.rows[0];
        const grossSalary = parseFloat(emp.salary);

        // Calculate taxes
        const shif = calculateSHIF(grossSalary);
        const nssf = calculateNSSF(grossSalary, employment_type);
        const paye = calculatePAYE(grossSalary);
        const housingLevy = calculateHousingLevy(grossSalary);

        const totalDeductions = shif + nssf + paye;
        const netSalary = grossSalary - totalDeductions;
        const employerNSSF = employment_type !== 'CASUAL' ? nssf : 0;

        calculations.push({
          employeeId: emp.employeeId,
          employeeName: `${first_name} ${last_name}`,
          employmentType: employment_type,
          grossSalary,
          deductions: {
            shif: Math.round(shif * 100) / 100,
            nssf: Math.round(nssf * 100) / 100,
            paye: Math.round(paye * 100) / 100,
            total: Math.round(totalDeductions * 100) / 100,
          },
          employerContributions: {
            nssf: Math.round(employerNSSF * 100) / 100,
            housingLevy: Math.round(housingLevy * 100) / 100,
          },
          netSalary: Math.round(netSalary * 100) / 100,
        });
      } catch (error) {
        calculations.push({
          employeeId: emp.employeeId,
          error: 'Calculation error',
        });
      }
    }

    // Calculate totals
    const totals = calculations.reduce(
      (acc, calc) => {
        if (!calc.error) {
          acc.totalGross += calc.grossSalary;
          acc.totalNet += calc.netSalary;
          acc.totalSHIF += calc.deductions.shif;
          acc.totalNSSF += calc.deductions.nssf;
          acc.totalPAYE += calc.deductions.paye;
          acc.totalHousingLevy += calc.employerContributions.housingLevy;
          acc.employeeCount++;
        }
        return acc;
      },
      {
        totalGross: 0,
        totalNet: 0,
        totalSHIF: 0,
        totalNSSF: 0,
        totalPAYE: 0,
        totalHousingLevy: 0,
        employeeCount: 0,
      }
    );

    res.json({
      success: true,
      data: {
        calculations,
        totals: {
          ...totals,
          totalGross: Math.round(totals.totalGross * 100) / 100,
          totalNet: Math.round(totals.totalNet * 100) / 100,
          totalSHIF: Math.round(totals.totalSHIF * 100) / 100,
          totalNSSF: Math.round(totals.totalNSSF * 100) / 100,
          totalPAYE: Math.round(totals.totalPAYE * 100) / 100,
          totalHousingLevy: Math.round(totals.totalHousingLevy * 100) / 100,
        },
      },
    });
  } catch (error) {
    console.error('Bulk calculate tax error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// @route   GET /api/tax/compliance-check
// @desc    Check tax compliance for organization
// @access  Private
router.get('/compliance-check', async (req, res) => {
  try {
    const { year, month } = req.query;
    const currentYear = year || new Date().getFullYear();
    const currentMonth = month || new Date().getMonth() + 1;

    // Get payroll records for the specified period
    const payrollQuery = `
      SELECT 
        COUNT(*) as total_employees,
        SUM(paye_tax) as total_paye,
        SUM(nssf_employee) as total_nssf_employee,
        SUM(nssf_employer) as total_nssf_employer,
        SUM(shif_contribution) as total_shif,
        SUM(housing_levy) as total_housing_levy
      FROM payroll_records pr
      JOIN payroll_periods pp ON pr.payroll_period_id = pp.id
      WHERE EXTRACT(YEAR FROM pp.start_date) = $1
      AND EXTRACT(MONTH FROM pp.start_date) = $2
    `;

    const payrollResult = await query(payrollQuery, [currentYear, currentMonth]);
    const payrollData = payrollResult.rows[0];

    // Check compliance status
    const compliance = {
      period: `${currentYear}-${currentMonth.toString().padStart(2, '0')}`,
      status: 'COMPLIANT',
      checks: [
        {
          item: 'SHIF Deductions',
          status: payrollData.total_shif > 0 ? 'PASS' : 'FAIL',
          amount: parseFloat(payrollData.total_shif || 0),
          description: 'All employees should have SHIF deductions',
        },
        {
          item: 'NSSF Deductions',
          status: payrollData.total_nssf_employee > 0 ? 'PASS' : 'WARNING',
          amount: parseFloat(payrollData.total_nssf_employee || 0),
          description: 'Permanent and contract employees should have NSSF deductions',
        },
        {
          item: 'PAYE Deductions',
          status: payrollData.total_paye >= 0 ? 'PASS' : 'FAIL',
          amount: parseFloat(payrollData.total_paye || 0),
          description: 'PAYE should be calculated based on tax bands',
        },
        {
          item: 'Housing Levy',
          status: payrollData.total_housing_levy > 0 ? 'PASS' : 'WARNING',
          amount: parseFloat(payrollData.total_housing_levy || 0),
          description: 'Employer should contribute to Housing Development Levy',
        },
      ],
      summary: {
        totalEmployees: parseInt(payrollData.total_employees || 0),
        totalPAYE: parseFloat(payrollData.total_paye || 0),
        totalSHIF: parseFloat(payrollData.total_shif || 0),
        totalNSSF: parseFloat(payrollData.total_nssf_employee || 0),
        totalHousingLevy: parseFloat(payrollData.total_housing_levy || 0),
      },
    };

    // Determine overall compliance status
    const hasFailures = compliance.checks.some(check => check.status === 'FAIL');
    const hasWarnings = compliance.checks.some(check => check.status === 'WARNING');

    if (hasFailures) {
      compliance.status = 'NON_COMPLIANT';
    } else if (hasWarnings) {
      compliance.status = 'PARTIAL_COMPLIANCE';
    }

    res.json({
      success: true,
      data: {
        compliance,
      },
    });
  } catch (error) {
    console.error('Tax compliance check error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

export default router;
