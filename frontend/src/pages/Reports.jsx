import React, { useState } from 'react';
import { useQuery } from 'react-query';
import { FileText, Download, Calendar, TrendingUp } from 'lucide-react';
import { reportsAPI } from '../services/api';
import Card from '../components/UI/Card';
import Button from '../components/UI/Button';
import LoadingSpinner from '../components/UI/LoadingSpinner';

const Reports = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('current-month');
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());

  const { data: taxSummaryData, isLoading: taxLoading } = useQuery(
    ['tax-summary', selectedYear],
    () => reportsAPI.getTaxSummary({ year: selectedYear }),
    {
      select: (response) => response.data.data,
    }
  );

  const { data: payrollSummaryData, isLoading: payrollLoading } = useQuery(
    ['payroll-summary', selectedPeriod],
    () => reportsAPI.getPayrollSummary({}),
    {
      select: (response) => response.data.data,
    }
  );

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount || 0);
  };

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 5 }, (_, i) => currentYear - i);

  if (taxLoading || payrollLoading) {
    return <LoadingSpinner text="Loading reports..." />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            📈 Reports & Analytics
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Comprehensive payroll and tax reports for your organization
          </p>
        </div>
        <div className="mt-4 flex space-x-3 md:mt-0 md:ml-4">
          <Button variant="outline" className="flex items-center">
            <Download className="h-4 w-4 mr-2" />
            Export PDF
          </Button>
          <Button variant="outline" className="flex items-center">
            <Download className="h-4 w-4 mr-2" />
            Export Excel
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Report Period
            </label>
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="current-month">Current Month</option>
              <option value="last-month">Last Month</option>
              <option value="current-quarter">Current Quarter</option>
              <option value="current-year">Current Year</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Year
            </label>
            <select
              value={selectedYear}
              onChange={(e) => setSelectedYear(parseInt(e.target.value))}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            >
              {years.map((year) => (
                <option key={year} value={year}>
                  {year}
                </option>
              ))}
            </select>
          </div>
          <div className="flex items-end">
            <Button className="w-full">Generate Report</Button>
          </div>
        </div>
      </Card>

      {/* Tax Summary */}
      <Card>
        <Card.Header>
          <Card.Title className="flex items-center">
            <TrendingUp className="h-5 w-5 mr-2" />
            Tax Summary ({selectedYear})
          </Card.Title>
        </Card.Header>
        <Card.Content>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {formatCurrency(taxSummaryData?.summary?.total_paye)}
              </div>
              <div className="text-sm text-gray-500">Total PAYE</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(taxSummaryData?.summary?.total_shif)}
              </div>
              <div className="text-sm text-gray-500">Total SHIF</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {formatCurrency(taxSummaryData?.summary?.total_nssf_employee)}
              </div>
              <div className="text-sm text-gray-500">Employee NSSF</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {formatCurrency(taxSummaryData?.summary?.total_housing_levy)}
              </div>
              <div className="text-sm text-gray-500">Housing Levy</div>
            </div>
          </div>

          {/* Monthly Breakdown */}
          {taxSummaryData?.monthlyBreakdown && taxSummaryData.monthlyBreakdown.length > 0 && (
            <div className="mt-8">
              <h4 className="text-lg font-medium text-gray-900 mb-4">Monthly Breakdown</h4>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Month
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        PAYE
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        SHIF
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        NSSF
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Housing Levy
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {taxSummaryData.monthlyBreakdown.map((month) => (
                      <tr key={month.month}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {new Date(2023, month.month - 1).toLocaleString('default', { month: 'long' })}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(month.paye)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(month.shif)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(month.nssf_employee)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(month.housing_levy)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </Card.Content>
      </Card>

      {/* Department Summary */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <Card.Header>
            <Card.Title>Department Summary</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="space-y-3">
              {payrollSummaryData?.departmentBreakdown?.map((dept) => (
                <div key={dept.department_name} className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">{dept.department_name}</span>
                  <div className="text-right">
                    <div className="text-sm font-medium">{dept.employee_count} employees</div>
                    <div className="text-xs text-gray-500">
                      {formatCurrency(dept.total_gross)}
                    </div>
                  </div>
                </div>
              ))}
              {(!payrollSummaryData?.departmentBreakdown || payrollSummaryData.departmentBreakdown.length === 0) && (
                <p className="text-sm text-gray-500 text-center py-4">
                  No department data available
                </p>
              )}
            </div>
          </Card.Content>
        </Card>

        <Card>
          <Card.Header>
            <Card.Title>Employment Type Summary</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="space-y-3">
              {payrollSummaryData?.employmentTypeBreakdown?.map((type) => (
                <div key={type.employment_type} className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 capitalize">
                    {type.employment_type.toLowerCase()}
                  </span>
                  <div className="text-right">
                    <div className="text-sm font-medium">{type.employee_count} employees</div>
                    <div className="text-xs text-gray-500">
                      {formatCurrency(type.total_gross)}
                    </div>
                  </div>
                </div>
              ))}
              {(!payrollSummaryData?.employmentTypeBreakdown || payrollSummaryData.employmentTypeBreakdown.length === 0) && (
                <p className="text-sm text-gray-500 text-center py-4">
                  No employment type data available
                </p>
              )}
            </div>
          </Card.Content>
        </Card>
      </div>

      {/* Compliance Status */}
      <Card>
        <Card.Header>
          <Card.Title className="flex items-center">
            🇰🇪 KRA Compliance Status
          </Card.Title>
        </Card.Header>
        <Card.Content>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-3">✅ Compliance Checklist</h4>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center text-green-600">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  SHIF deductions calculated correctly
                </li>
                <li className="flex items-center text-green-600">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  NSSF contributions up to date
                </li>
                <li className="flex items-center text-green-600">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  PAYE calculated per Finance Act 2023
                </li>
                <li className="flex items-center text-green-600">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  Housing Levy contributions current
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-3">📋 Required Actions</h4>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center text-blue-600">
                  <Calendar className="h-4 w-4 mr-2" />
                  Monthly PAYE returns due 9th of following month
                </li>
                <li className="flex items-center text-blue-600">
                  <Calendar className="h-4 w-4 mr-2" />
                  NSSF returns due 15th of following month
                </li>
                <li className="flex items-center text-blue-600">
                  <Calendar className="h-4 w-4 mr-2" />
                  SHIF returns due 9th of following month
                </li>
                <li className="flex items-center text-blue-600">
                  <Calendar className="h-4 w-4 mr-2" />
                  Housing Levy returns quarterly
                </li>
              </ul>
            </div>
          </div>
        </Card.Content>
      </Card>
    </div>
  );
};

export default Reports;
