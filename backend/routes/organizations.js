import express from 'express';
import { body, validationResult, param } from 'express-validator';
import { query } from '../config/database.js';

const router = express.Router();

// Validation rules
const organizationValidation = [
  body('name').notEmpty().withMessage('Organization name is required'),
  body('organizationType').isIn(['GOVERNMENT', 'PRIVATE', 'NGO', 'PARASTATAL']).withMessage('Invalid organization type'),
  body('email').optional().isEmail().withMessage('Valid email is required'),
  body('phone').optional().isMobilePhone().withMessage('Valid phone number is required'),
];

// @route   GET /api/organizations
// @desc    Get all organizations
// @access  Private
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '', type = '' } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE 1=1';
    const params = [];
    let paramCount = 0;

    if (search) {
      paramCount++;
      whereClause += ` AND name ILIKE $${paramCount}`;
      params.push(`%${search}%`);
    }

    if (type) {
      paramCount++;
      whereClause += ` AND organization_type = $${paramCount}`;
      params.push(type);
    }

    const organizationsQuery = `
      SELECT 
        id, name, organization_type, address, phone, email,
        website, logo_path, tax_pin, registration_number,
        created_at, updated_at
      FROM organizations
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `;

    params.push(limit, offset);

    const organizations = await query(organizationsQuery, params);

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM organizations
      ${whereClause}
    `;
    const countParams = params.slice(0, -2);
    const totalResult = await query(countQuery, countParams);
    const total = parseInt(totalResult.rows[0].total);

    res.json({
      success: true,
      data: {
        organizations: organizations.rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error('Get organizations error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// @route   GET /api/organizations/:id
// @desc    Get organization by ID
// @access  Private
router.get('/:id', param('id').isInt(), async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid organization ID',
        errors: errors.array(),
      });
    }

    const { id } = req.params;

    const organizationQuery = `
      SELECT 
        id, name, organization_type, address, phone, email,
        website, logo_path, tax_pin, registration_number,
        created_at, updated_at
      FROM organizations
      WHERE id = $1
    `;

    const result = await query(organizationQuery, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Organization not found',
      });
    }

    res.json({
      success: true,
      data: {
        organization: result.rows[0],
      },
    });
  } catch (error) {
    console.error('Get organization error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// @route   POST /api/organizations
// @desc    Create new organization
// @access  Private (Admin only)
router.post('/', organizationValidation, async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isSuperuser) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin privileges required.',
      });
    }

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    const {
      name,
      organizationType,
      address,
      phone,
      email,
      website,
      logoPath,
      taxPin,
      registrationNumber,
    } = req.body;

    // Check if organization name already exists
    const existingOrg = await query(
      'SELECT id FROM organizations WHERE name = $1',
      [name]
    );

    if (existingOrg.rows.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Organization with this name already exists',
      });
    }

    const insertQuery = `
      INSERT INTO organizations (
        name, organization_type, address, phone, email,
        website, logo_path, tax_pin, registration_number
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9
      ) RETURNING id, name, organization_type, created_at
    `;

    const result = await query(insertQuery, [
      name, organizationType, address, phone, email,
      website, logoPath, taxPin, registrationNumber
    ]);

    res.status(201).json({
      success: true,
      message: 'Organization created successfully',
      data: {
        organization: result.rows[0],
      },
    });
  } catch (error) {
    console.error('Create organization error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// @route   PUT /api/organizations/:id
// @desc    Update organization
// @access  Private (Admin only)
router.put('/:id', [param('id').isInt(), ...organizationValidation], async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isSuperuser) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin privileges required.',
      });
    }

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    const { id } = req.params;
    const {
      name,
      organizationType,
      address,
      phone,
      email,
      website,
      logoPath,
      taxPin,
      registrationNumber,
    } = req.body;

    // Check if organization exists
    const existingOrg = await query('SELECT id FROM organizations WHERE id = $1', [id]);
    if (existingOrg.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Organization not found',
      });
    }

    // Check if name already exists for another organization
    const duplicateCheck = await query(
      'SELECT id FROM organizations WHERE name = $1 AND id != $2',
      [name, id]
    );

    if (duplicateCheck.rows.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Organization with this name already exists',
      });
    }

    const updateQuery = `
      UPDATE organizations SET
        name = $1, organization_type = $2, address = $3,
        phone = $4, email = $5, website = $6, logo_path = $7,
        tax_pin = $8, registration_number = $9,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $10
    `;

    await query(updateQuery, [
      name, organizationType, address, phone, email,
      website, logoPath, taxPin, registrationNumber, id
    ]);

    res.json({
      success: true,
      message: 'Organization updated successfully',
    });
  } catch (error) {
    console.error('Update organization error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// @route   DELETE /api/organizations/:id
// @desc    Delete organization
// @access  Private (Admin only)
router.delete('/:id', param('id').isInt(), async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isSuperuser) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin privileges required.',
      });
    }

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid organization ID',
        errors: errors.array(),
      });
    }

    const { id } = req.params;

    // Check if organization exists
    const existingOrg = await query('SELECT id FROM organizations WHERE id = $1', [id]);
    if (existingOrg.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Organization not found',
      });
    }

    // Check if organization has employees
    const employeeCheck = await query(
      'SELECT COUNT(*) as count FROM employees WHERE department_id IN (SELECT id FROM departments WHERE organization_id = $1)',
      [id]
    );

    if (parseInt(employeeCheck.rows[0].count) > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete organization with existing employees',
      });
    }

    await query('DELETE FROM organizations WHERE id = $1', [id]);

    res.json({
      success: true,
      message: 'Organization deleted successfully',
    });
  } catch (error) {
    console.error('Delete organization error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

export default router;
