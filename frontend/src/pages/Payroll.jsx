import React, { useState, useEffect } from 'react';
import { useQuery } from 'react-query';
import { useSearchParams } from 'react-router-dom';
import { Calculator, DollarSign, FileText, TrendingUp } from 'lucide-react';
import { employeesAPI, payrollAPI } from '../services/api';
import Card from '../components/UI/Card';
import Button from '../components/UI/Button';
import LoadingSpinner from '../components/UI/LoadingSpinner';

const Payroll = () => {
  const [searchParams] = useSearchParams();
  const [selectedEmployeeId, setSelectedEmployeeId] = useState(searchParams.get('employee') || '');
  const [basicSalary, setBasicSalary] = useState(50000);
  const [employmentType, setEmploymentType] = useState('PERMANENT');
  const [allowances, setAllowances] = useState(0);
  const [deductions, setDeductions] = useState(0);
  const [payrollResult, setPayrollResult] = useState(null);

  const { data: employeesData } = useQuery(
    'employees-all',
    () => employeesAPI.getAll({ limit: 1000 }),
    {
      select: (response) => response.data.data.employees,
    }
  );

  // Calculate payroll when inputs change
  useEffect(() => {
    if (basicSalary > 0) {
      calculatePayroll();
    }
  }, [basicSalary, employmentType, allowances, deductions]);

  // Load selected employee data
  useEffect(() => {
    if (selectedEmployeeId && employeesData) {
      const employee = employeesData.find(emp => emp.id == selectedEmployeeId);
      if (employee) {
        setBasicSalary(employee.basic_salary || 50000);
        setEmploymentType(employee.employment_type || 'PERMANENT');
      }
    }
  }, [selectedEmployeeId, employeesData]);

  const calculateSHIF = (salary) => {
    return salary * 0.0275; // 2.75% SHIF contribution
  };

  const calculateNSSF = (salary, empType) => {
    if (empType === 'CASUAL') return 0;
    return salary * 0.06; // 6% NSSF contribution
  };

  const calculatePAYE = (salary) => {
    // Kenyan PAYE tax bands (Finance Act 2023)
    if (salary <= 24000) return 0;
    if (salary <= 32333) return (salary - 24000) * 0.1;
    if (salary <= 40385) return 833.3 + (salary - 32333) * 0.15;
    if (salary <= 48462) return 2041.1 + (salary - 40385) * 0.2;
    if (salary <= 56538) return 3656.5 + (salary - 48462) * 0.25;
    return 4675.5 + (salary - 56538) * 0.3;
  };

  const calculateHousingLevy = (salary) => {
    return salary * 0.015; // 1.5% Housing Levy (employer contribution)
  };

  const calculatePayroll = () => {
    const grossSalary = parseFloat(basicSalary) + parseFloat(allowances);
    const shif = calculateSHIF(grossSalary);
    const nssf = calculateNSSF(grossSalary, employmentType);
    const paye = calculatePAYE(grossSalary);
    const housingLevy = calculateHousingLevy(grossSalary);

    const totalDeductions = shif + nssf + paye + parseFloat(deductions);
    const netSalary = grossSalary - totalDeductions;

    // Employer contributions
    const employerNSSF = employmentType !== 'CASUAL' ? nssf : 0;

    setPayrollResult({
      basicSalary: parseFloat(basicSalary),
      allowances: parseFloat(allowances),
      grossSalary,
      deductions: {
        shif: Math.round(shif * 100) / 100,
        nssf: Math.round(nssf * 100) / 100,
        paye: Math.round(paye * 100) / 100,
        other: parseFloat(deductions),
        total: Math.round(totalDeductions * 100) / 100,
      },
      employerContributions: {
        nssf: Math.round(employerNSSF * 100) / 100,
        housingLevy: Math.round(housingLevy * 100) / 100,
      },
      netSalary: Math.round(netSalary * 100) / 100,
      employmentType,
    });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount || 0);
  };

  const selectedEmployee = employeesData?.find(emp => emp.id == selectedEmployeeId);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            💰 Payroll Processing
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Calculate payroll with Kenyan tax compliance (SHIF, NSSF, PAYE, Housing Levy)
          </p>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4">
          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
            🇰🇪 KRA Compliant
          </span>
        </div>
      </div>

      {/* Payroll Calculator */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Input Form */}
        <Card>
          <Card.Header>
            <Card.Title className="flex items-center">
              <Calculator className="h-5 w-5 mr-2" />
              Payroll Calculator
            </Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="space-y-4">
              {/* Employee Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Select Employee
                </label>
                <select
                  value={selectedEmployeeId}
                  onChange={(e) => setSelectedEmployeeId(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">Choose an employee...</option>
                  {employeesData?.map((employee) => (
                    <option key={employee.id} value={employee.id}>
                      {employee.first_name} {employee.last_name} - {employee.employee_number}
                    </option>
                  ))}
                </select>
                {selectedEmployee && (
                  <div className="mt-2 p-2 bg-blue-50 rounded text-sm">
                    <p><strong>Department:</strong> {selectedEmployee.department_name || 'N/A'}</p>
                    <p><strong>Job Title:</strong> {selectedEmployee.job_title || 'N/A'}</p>
                  </div>
                )}
              </div>

              {/* Basic Salary */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Basic Salary (KES)
                </label>
                <input
                  type="number"
                  value={basicSalary}
                  onChange={(e) => setBasicSalary(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  min="0"
                  step="100"
                />
              </div>

              {/* Employment Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Employment Type
                </label>
                <select
                  value={employmentType}
                  onChange={(e) => setEmploymentType(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="PERMANENT">Permanent</option>
                  <option value="CONTRACT">Contract</option>
                  <option value="CASUAL">Casual Worker</option>
                </select>
              </div>

              {/* Allowances */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Allowances (KES)
                </label>
                <input
                  type="number"
                  value={allowances}
                  onChange={(e) => setAllowances(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  min="0"
                  step="100"
                />
              </div>

              {/* Other Deductions */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Other Deductions (KES)
                </label>
                <input
                  type="number"
                  value={deductions}
                  onChange={(e) => setDeductions(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  min="0"
                  step="100"
                />
              </div>
            </div>
          </Card.Content>
        </Card>

        {/* Results */}
        <Card>
          <Card.Header>
            <Card.Title className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Payroll Calculation Results
            </Card.Title>
          </Card.Header>
          <Card.Content>
            {payrollResult ? (
              <div className="space-y-4">
                {/* Gross Salary */}
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="font-medium text-blue-900">Gross Salary:</span>
                    <span className="text-lg font-bold text-blue-900">
                      {formatCurrency(payrollResult.grossSalary)}
                    </span>
                  </div>
                  <div className="text-sm text-blue-700 mt-1">
                    Basic: {formatCurrency(payrollResult.basicSalary)} + 
                    Allowances: {formatCurrency(payrollResult.allowances)}
                  </div>
                </div>

                {/* Deductions */}
                <div className="space-y-2">
                  <h4 className="font-medium text-gray-900">Statutory Deductions:</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between text-red-600">
                      <span>SHIF (2.75%):</span>
                      <span>{formatCurrency(payrollResult.deductions.shif)}</span>
                    </div>
                    <div className="flex justify-between text-red-600">
                      <span>NSSF (6%):</span>
                      <span>{formatCurrency(payrollResult.deductions.nssf)}</span>
                    </div>
                    <div className="flex justify-between text-red-600">
                      <span>PAYE:</span>
                      <span>{formatCurrency(payrollResult.deductions.paye)}</span>
                    </div>
                    {payrollResult.deductions.other > 0 && (
                      <div className="flex justify-between text-red-600">
                        <span>Other Deductions:</span>
                        <span>{formatCurrency(payrollResult.deductions.other)}</span>
                      </div>
                    )}
                    <hr className="my-2" />
                    <div className="flex justify-between font-medium text-red-700">
                      <span>Total Deductions:</span>
                      <span>{formatCurrency(payrollResult.deductions.total)}</span>
                    </div>
                  </div>
                </div>

                {/* Net Salary */}
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="font-medium text-green-900">Net Salary:</span>
                    <span className="text-xl font-bold text-green-900">
                      {formatCurrency(payrollResult.netSalary)}
                    </span>
                  </div>
                </div>

                {/* Employer Contributions */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">Employer Contributions:</h4>
                  <div className="space-y-1 text-sm text-gray-700">
                    <div className="flex justify-between">
                      <span>Employer NSSF:</span>
                      <span>{formatCurrency(payrollResult.employerContributions.nssf)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Housing Levy (1.5%):</span>
                      <span>{formatCurrency(payrollResult.employerContributions.housingLevy)}</span>
                    </div>
                  </div>
                </div>

                {/* Employment Type Note */}
                <div className="text-xs text-gray-600 bg-yellow-50 p-3 rounded">
                  <strong>Note:</strong> {employmentType === 'CASUAL' 
                    ? 'Casual workers are exempt from NSSF contributions but subject to Housing Levy.'
                    : 'All statutory deductions apply for permanent and contract employees.'}
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Calculator className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>Enter salary details to see calculation results</p>
              </div>
            )}
          </Card.Content>
        </Card>
      </div>

      {/* Tax Information */}
      <Card>
        <Card.Header>
          <Card.Title className="flex items-center">
            🇰🇪 Kenyan Tax Information
          </Card.Title>
        </Card.Header>
        <Card.Content>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Current Tax Rates (2023)</h4>
              <ul className="space-y-2 text-sm">
                <li className="flex justify-between">
                  <span>SHIF:</span>
                  <span className="font-medium">2.75%</span>
                </li>
                <li className="flex justify-between">
                  <span>NSSF (Employee):</span>
                  <span className="font-medium">6%</span>
                </li>
                <li className="flex justify-between">
                  <span>NSSF (Employer):</span>
                  <span className="font-medium">6%</span>
                </li>
                <li className="flex justify-between">
                  <span>Housing Levy:</span>
                  <span className="font-medium">1.5%</span>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-3">PAYE Tax Bands</h4>
              <ul className="space-y-1 text-sm">
                <li>KES 0 - 24,000: 0%</li>
                <li>KES 24,001 - 32,333: 10%</li>
                <li>KES 32,334 - 40,385: 15%</li>
                <li>KES 40,386 - 48,462: 20%</li>
                <li>KES 48,463 - 56,538: 25%</li>
                <li>Above KES 56,538: 30%</li>
              </ul>
            </div>
          </div>
        </Card.Content>
      </Card>
    </div>
  );
};

export default Payroll;
